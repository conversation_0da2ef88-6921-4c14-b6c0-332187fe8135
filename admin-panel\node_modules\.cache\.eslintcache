[{"D:\\project\\HNrealstate\\admin-panel\\src\\index.tsx": "1", "D:\\project\\HNrealstate\\admin-panel\\src\\App.tsx": "2", "D:\\project\\HNrealstate\\admin-panel\\src\\store\\store.ts": "3", "D:\\project\\HNrealstate\\admin-panel\\src\\pages\\Dashboard.tsx": "4", "D:\\project\\HNrealstate\\admin-panel\\src\\pages\\Login.tsx": "5", "D:\\project\\HNrealstate\\admin-panel\\src\\pages\\Users.tsx": "6", "D:\\project\\HNrealstate\\admin-panel\\src\\pages\\Properties.tsx": "7", "D:\\project\\HNrealstate\\admin-panel\\src\\pages\\Media.tsx": "8", "D:\\project\\HNrealstate\\admin-panel\\src\\components\\ProtectedRoute.tsx": "9", "D:\\project\\HNrealstate\\admin-panel\\src\\components\\Layout.tsx": "10", "D:\\project\\HNrealstate\\admin-panel\\src\\store\\slices\\authSlice.ts": "11", "D:\\project\\HNrealstate\\admin-panel\\src\\store\\slices\\userSlice.ts": "12", "D:\\project\\HNrealstate\\admin-panel\\src\\store\\slices\\propertySlice.ts": "13"}, {"size": 273, "mtime": 1759580316983, "results": "14", "hashOfConfig": "15"}, {"size": 2446, "mtime": 1759596920278, "results": "16", "hashOfConfig": "15"}, {"size": 447, "mtime": 1759580321972, "results": "17", "hashOfConfig": "15"}, {"size": 11082, "mtime": 1759596665454, "results": "18", "hashOfConfig": "15"}, {"size": 7051, "mtime": 1759596633151, "results": "19", "hashOfConfig": "15"}, {"size": 14575, "mtime": 1759596802394, "results": "20", "hashOfConfig": "15"}, {"size": 18102, "mtime": 1759596748435, "results": "21", "hashOfConfig": "15"}, {"size": 16803, "mtime": 1759596865980, "results": "22", "hashOfConfig": "15"}, {"size": 2943, "mtime": 1759596969653, "results": "23", "hashOfConfig": "15"}, {"size": 8187, "mtime": 1759596956458, "results": "24", "hashOfConfig": "15"}, {"size": 4419, "mtime": 1759596518542, "results": "25", "hashOfConfig": "15"}, {"size": 7629, "mtime": 1759596597035, "results": "26", "hashOfConfig": "15"}, {"size": 6630, "mtime": 1759596539714, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1er2ied", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\project\\HNrealstate\\admin-panel\\src\\index.tsx", [], [], "D:\\project\\HNrealstate\\admin-panel\\src\\App.tsx", [], [], "D:\\project\\HNrealstate\\admin-panel\\src\\store\\store.ts", [], [], "D:\\project\\HNrealstate\\admin-panel\\src\\pages\\Dashboard.tsx", ["67", "68"], [], "D:\\project\\HNrealstate\\admin-panel\\src\\pages\\Login.tsx", [], [], "D:\\project\\HNrealstate\\admin-panel\\src\\pages\\Users.tsx", ["69", "70"], [], "D:\\project\\HNrealstate\\admin-panel\\src\\pages\\Properties.tsx", ["71", "72", "73", "74"], [], "D:\\project\\HNrealstate\\admin-panel\\src\\pages\\Media.tsx", ["75", "76", "77", "78"], [], "D:\\project\\HNrealstate\\admin-panel\\src\\components\\ProtectedRoute.tsx", [], [], "D:\\project\\HNrealstate\\admin-panel\\src\\components\\Layout.tsx", [], [], "D:\\project\\HNrealstate\\admin-panel\\src\\store\\slices\\authSlice.ts", [], [], "D:\\project\\HNrealstate\\admin-panel\\src\\store\\slices\\userSlice.ts", [], [], "D:\\project\\HNrealstate\\admin-panel\\src\\store\\slices\\propertySlice.ts", [], [], {"ruleId": "79", "severity": 1, "message": "80", "line": 17, "column": 3, "nodeType": "81", "messageId": "82", "endLine": 17, "endColumn": 13}, {"ruleId": "79", "severity": 1, "message": "83", "line": 18, "column": 3, "nodeType": "81", "messageId": "82", "endLine": 18, "endColumn": 13}, {"ruleId": "79", "severity": 1, "message": "84", "line": 36, "column": 3, "nodeType": "81", "messageId": "82", "endLine": 36, "endColumn": 9}, {"ruleId": "79", "severity": 1, "message": "85", "line": 52, "column": 27, "nodeType": "81", "messageId": "82", "endLine": 52, "endColumn": 32}, {"ruleId": "79", "severity": 1, "message": "86", "line": 34, "column": 3, "nodeType": "81", "messageId": "82", "endLine": 34, "endColumn": 13}, {"ruleId": "79", "severity": 1, "message": "87", "line": 35, "column": 3, "nodeType": "81", "messageId": "82", "endLine": 35, "endColumn": 7}, {"ruleId": "79", "severity": 1, "message": "88", "line": 37, "column": 3, "nodeType": "81", "messageId": "82", "endLine": 37, "endColumn": 14}, {"ruleId": "79", "severity": 1, "message": "85", "line": 52, "column": 32, "nodeType": "81", "messageId": "82", "endLine": 52, "endColumn": 37}, {"ruleId": "79", "severity": 1, "message": "89", "line": 16, "column": 3, "nodeType": "81", "messageId": "82", "endLine": 16, "endColumn": 6}, {"ruleId": "79", "severity": 1, "message": "90", "line": 27, "column": 3, "nodeType": "81", "messageId": "82", "endLine": 27, "endColumn": 6}, {"ruleId": "91", "severity": 1, "message": "92", "line": 56, "column": 6, "nodeType": "93", "endLine": 56, "endColumn": 8, "suggestions": "94"}, {"ruleId": "79", "severity": 1, "message": "95", "line": 80, "column": 15, "nodeType": "81", "messageId": "82", "endLine": 80, "endColumn": 23}, "@typescript-eslint/no-unused-vars", "'TrendingUp' is defined but never used.", "Identifier", "unusedVar", "'Visibility' is defined but never used.", "'People' is defined but never used.", "'error' is assigned a value but never used.", "'FilterList' is defined but never used.", "'Home' is defined but never used.", "'AttachMoney' is defined but never used.", "'Fab' is defined but never used.", "'Add' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchFiles'. Either include it or remove the dependency array.", "ArrayExpression", ["96"], "'response' is assigned a value but never used.", {"desc": "97", "fix": "98"}, "Update the dependencies array to be: [fetchFiles]", {"range": "99", "text": "100"}, [1345, 1347], "[fetchFiles]"]